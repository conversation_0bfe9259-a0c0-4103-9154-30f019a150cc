#!/bin/bash

# Anniversary.mp4 压缩进度监控脚本

echo "🎬 Anniversary.mp4 多版本压缩进度监控"
echo "========================================"

# 原始文件大小
original_size=$(stat -c%s "src/client/assets/videos/anniversary/anniversary.mp4" 2>/dev/null || echo "0")
original_mb=$(echo "scale=1; $original_size / 1024 / 1024" | bc 2>/dev/null || echo "570")

echo "📊 原始文件: ${original_mb}MB"
echo ""

while true; do
    clear
    echo "🎬 Anniversary.mp4 多版本压缩进度监控"
    echo "========================================"
    echo "📊 原始文件: ${original_mb}MB"
    echo ""
    
    # 检查进程状态
    ffmpeg_count=$(ps aux | grep ffmpeg | grep anniversary | grep -v grep | wc -l)
    echo "🔄 活跃压缩进程: $ffmpeg_count"
    echo ""
    
    # 显示文件大小和压缩率
    echo "📁 当前文件大小:"
    if [ -d "src/client/assets/videos-compressed/anniversary-test" ]; then
        for file in src/client/assets/videos-compressed/anniversary-test/*.mp4; do
            if [ -f "$file" ]; then
                filename=$(basename "$file")
                size=$(stat -c%s "$file" 2>/dev/null || echo "0")
                size_mb=$(echo "scale=1; $size / 1024 / 1024" | bc 2>/dev/null || echo "0")
                
                if [ "$original_size" -gt 0 ]; then
                    compression_ratio=$(echo "scale=1; (1 - $size / $original_size) * 100" | bc 2>/dev/null || echo "0")
                else
                    compression_ratio="0"
                fi
                
                printf "  %-25s %8s MB (压缩率: %5s%%)\n" "$filename" "$size_mb" "$compression_ratio"
            fi
        done
    else
        echo "  测试目录尚未创建..."
    fi
    
    echo ""
    echo "⏱️  $(date '+%H:%M:%S') - 每10秒更新一次 (Ctrl+C 退出)"
    
    # 如果没有活跃进程，显示完成信息
    if [ "$ffmpeg_count" -eq 0 ]; then
        echo ""
        echo "✅ 所有压缩任务已完成！"
        echo ""
        echo "🎯 推荐选择："
        echo "  • 如需最高质量: anniversary_crf18.mp4"
        echo "  • 如需平衡效果: anniversary_crf21.mp4 或 anniversary_crf23_2pass.mp4"
        echo "  • 如需小文件: anniversary_crf25.mp4 或 anniversary_crf28.mp4"
        echo ""
        echo "📁 测试文件位置: src/client/assets/videos-compressed/anniversary-test/"
        break
    fi
    
    sleep 10
done
