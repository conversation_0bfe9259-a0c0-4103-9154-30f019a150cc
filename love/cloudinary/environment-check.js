#!/usr/bin/env node

/**
 * 完整环境检查脚本
 * 验证Love项目Cloudinary系统的所有组件
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔍 Love项目Cloudinary系统环境检查\n');

// 检查项目结构
function checkProjectStructure() {
    console.log('📁 检查项目结构...');
    
    const requiredPaths = [
        'config/config.js',
        'config/.env',
        'src/client/assets/videos',
        'cloudinary/compress-videos-for-cloudinary.sh',
        'cloudinary/test-connection.js'
    ];
    
    let allExists = true;
    
    requiredPaths.forEach(filePath => {
        if (fs.existsSync(filePath)) {
            console.log(`   ✅ ${filePath}`);
        } else {
            console.log(`   ❌ ${filePath} - 缺失`);
            allExists = false;
        }
    });
    
    return allExists;
}

// 检查Node.js依赖
function checkDependencies() {
    console.log('\n📦 检查Node.js依赖...');
    
    try {
        const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
        const requiredDeps = ['cloudinary', 'dotenv', 'express'];
        
        let allInstalled = true;
        
        requiredDeps.forEach(dep => {
            if (packageJson.dependencies[dep]) {
                console.log(`   ✅ ${dep}: ${packageJson.dependencies[dep]}`);
            } else {
                console.log(`   ❌ ${dep} - 未安装`);
                allInstalled = false;
            }
        });
        
        return allInstalled;
    } catch (error) {
        console.log(`   ❌ 读取package.json失败: ${error.message}`);
        return false;
    }
}

// 检查ffmpeg
function checkFFmpeg() {
    console.log('\n🎬 检查FFmpeg...');
    
    try {
        const version = execSync('ffmpeg -version', { encoding: 'utf8' });
        const versionLine = version.split('\n')[0];
        console.log(`   ✅ ${versionLine}`);
        
        // 检查libx264支持
        if (version.includes('--enable-libx264')) {
            console.log('   ✅ libx264编码器支持');
            return true;
        } else {
            console.log('   ❌ libx264编码器不支持');
            return false;
        }
    } catch (error) {
        console.log(`   ❌ FFmpeg未安装或不可用: ${error.message}`);
        return false;
    }
}

// 检查配置文件
function checkConfiguration() {
    console.log('\n⚙️  检查配置文件...');
    
    try {
        const config = require('../config/config.js');
        
        console.log(`   ✅ 配置文件加载成功`);
        console.log(`   ✅ Cloudinary启用: ${config.cloudinary.enabled}`);
        console.log(`   ✅ 账户数量: ${Object.keys(config.cloudinary.accounts).length}`);
        console.log(`   ✅ 压缩CRF: ${config.cloudinary.compression.crf}`);
        console.log(`   ✅ 压缩预设: ${config.cloudinary.compression.speed}`);
        
        return true;
    } catch (error) {
        console.log(`   ❌ 配置文件加载失败: ${error.message}`);
        return false;
    }
}

// 检查环境变量
function checkEnvironmentVariables() {
    console.log('\n🔐 检查环境变量...');
    
    const requiredEnvVars = [
        'CLOUDINARY_ENABLED',
        'CLOUDINARY_SECRET_YU0',
        'CLOUDINARY_SECRET_YU1',
        'CLOUDINARY_SECRET_YU2',
        'CLOUDINARY_SECRET_YU3',
        'CLOUDINARY_SECRET_YU4',
        'CLOUDINARY_SECRET_YU5'
    ];
    
    let allSet = true;
    
    requiredEnvVars.forEach(envVar => {
        if (process.env[envVar]) {
            console.log(`   ✅ ${envVar}: 已设置`);
        } else {
            console.log(`   ❌ ${envVar}: 未设置`);
            allSet = false;
        }
    });
    
    return allSet;
}

// 主检查函数
async function runEnvironmentCheck() {
    const checks = [
        { name: '项目结构', fn: checkProjectStructure },
        { name: 'Node.js依赖', fn: checkDependencies },
        { name: 'FFmpeg', fn: checkFFmpeg },
        { name: '配置文件', fn: checkConfiguration },
        { name: '环境变量', fn: checkEnvironmentVariables }
    ];
    
    let allPassed = true;
    const results = [];
    
    for (const check of checks) {
        const result = check.fn();
        results.push({ name: check.name, passed: result });
        if (!result) allPassed = false;
    }
    
    // 汇总结果
    console.log('\n📊 环境检查结果汇总:');
    results.forEach(result => {
        const status = result.passed ? '✅' : '❌';
        console.log(`   ${status} ${result.name}`);
    });
    
    console.log('\n' + '='.repeat(50));
    
    if (allPassed) {
        console.log('🎉 环境检查全部通过！Love项目Cloudinary系统已就绪');
        process.exit(0);
    } else {
        console.log('⚠️  环境检查发现问题，请修复后重试');
        process.exit(1);
    }
}

// 运行检查
if (require.main === module) {
    runEnvironmentCheck();
}
